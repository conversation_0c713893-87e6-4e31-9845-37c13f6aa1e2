;;; message-actions.el --- Actions for chat messages -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides actions for chat messages, such as editing, regenerating,
;; copying, and deleting messages.

;;; Code:

(require 'cl-lib)
(require 'chat-customization) ; For buffer names
(require 'agents/agents-core) ; For ai-auto-complete-default-agent and core/backend
(require 'ui/streaming)       ; For ai-auto-complete-streaming-simulate

;; Customization options for message actions
(defgroup ai-auto-complete-message-actions nil
  "Settings for message actions in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-message-actions-")

(defcustom ai-auto-complete-message-actions-enabled t
  "Whether message actions are enabled."
  :type 'boolean
  :group 'ai-auto-complete-message-actions)

(defcustom ai-auto-complete-message-actions-buttons
  '(edit regenerate copy delete)
  "Buttons to show for message actions."
  :type '(set (const :tag "Edit" edit)
             (const :tag "Regenerate" regenerate)
             (const :tag "Copy" copy)
             (const :tag "Delete" delete))
  :group 'ai-auto-complete-message-actions)

;; Variables for tracking message actions state
(defvar-local ai-auto-complete-message-actions-editing nil
  "Whether a message is currently being edited.")

(defvar-local ai-auto-complete-message-actions-edit-marker nil
  "Marker for the current edit position.")

(defvar-local ai-auto-complete-message-actions-original-message nil
  "Original message being edited.")

(defvar-local ai-auto-complete-message-actions-edit-role nil
  "Role of the message being edited.")

;; Helper function to get the appropriate chat buffer
(defun ai-auto-complete-message-actions-get-chat-buffer ()
  "Get the appropriate chat buffer for message actions.
Returns the enhanced chat buffer if it exists and enhanced chat mode is active,
otherwise returns the regular chat buffer."
  (let* ((enhanced-chat-buffer (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
         (use-enhanced-chat-buffer
          (and enhanced-chat-buffer
               (buffer-live-p enhanced-chat-buffer)
               (with-current-buffer enhanced-chat-buffer
                 (and (boundp 'ai-auto-complete-enhanced-chat-mode)
                      ai-auto-complete-enhanced-chat-mode)))))
    (if use-enhanced-chat-buffer
        enhanced-chat-buffer
      (get-buffer-create ai-auto-complete-chat-buffer-name))))

(defvar-local ai-auto-complete-message-actions-edit-index nil
  "Index of the message being edited in the history.")

;; Define faces for message actions
(defface ai-auto-complete-message-actions-button-face
  '((t :box t :foreground "#729fcf" :background "#eeeeec" :height 0.8))
  "Face for message action buttons."
  :group 'ai-auto-complete-message-actions)

(defface ai-auto-complete-message-actions-edit-face
  '((t :background "#eeeeec" :foreground "#000000"))
  "Face for message being edited."
  :group 'ai-auto-complete-message-actions)

;; Function to insert action buttons for a message
(defun ai-auto-complete-message-actions-insert-buttons (role message index)
  "Insert action buttons for a message with ROLE and MESSAGE at INDEX."
  (when ai-auto-complete-message-actions-enabled
    (let ((inhibit-read-only t)
          (buttons-text ""))

      ;; Create buttons based on enabled actions
      (when (memq 'edit ai-auto-complete-message-actions-buttons)
        (setq buttons-text
              (concat buttons-text
                      (propertize " [Edit] "
                                 'face 'ai-auto-complete-message-actions-button-face
                                 'mouse-face 'highlight
                                 'help-echo "Edit this message"
                                 'ai-auto-complete-message-action 'edit
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-content message
                                 'ai-auto-complete-message-index index
                                 'button t
                                 'follow-link t
                                 'keymap (let ((map (make-sparse-keymap)))
                                          (define-key map [mouse-1] 'ai-auto-complete-message-actions-button-click)
                                          map)))))

      (when (memq 'regenerate ai-auto-complete-message-actions-buttons)
        (when (eq role 'assistant)
          (setq buttons-text
                (concat buttons-text
                        (propertize " [Regenerate] "
                                   'face 'ai-auto-complete-message-actions-button-face
                                   'mouse-face 'highlight
                                   'help-echo "Regenerate this response"
                                   'ai-auto-complete-message-action 'regenerate
                                   'ai-auto-complete-message-role role
                                   'ai-auto-complete-message-content message
                                   'ai-auto-complete-message-index index
                                   'button t
                                   'follow-link t
                                   'keymap (let ((map (make-sparse-keymap)))
                                            (define-key map [mouse-1] 'ai-auto-complete-message-actions-button-click)
                                            map))))))

      (when (memq 'copy ai-auto-complete-message-actions-buttons)
        (setq buttons-text
              (concat buttons-text
                      (propertize " [Copy] "
                                 'face 'ai-auto-complete-message-actions-button-face
                                 'mouse-face 'highlight
                                 'help-echo "Copy this message"
                                 'ai-auto-complete-message-action 'copy
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-content message
                                 'ai-auto-complete-message-index index
                                 'button t
                                 'follow-link t
                                 'keymap (let ((map (make-sparse-keymap)))
                                          (define-key map [mouse-1] 'ai-auto-complete-message-actions-button-click)
                                          map)))))

      (when (memq 'delete ai-auto-complete-message-actions-buttons)
        (setq buttons-text
              (concat buttons-text
                      (propertize " [Delete] "
                                 'face 'ai-auto-complete-message-actions-button-face
                                 'mouse-face 'highlight
                                 'help-echo "Delete this message"
                                 'ai-auto-complete-message-action 'delete
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-content message
                                 'ai-auto-complete-message-index index
                                 'button t
                                 'follow-link t
                                 'keymap (let ((map (make-sparse-keymap)))
                                          (define-key map [mouse-1] 'ai-auto-complete-message-actions-button-click)
                                          map)))))

      ;; Insert the buttons
      (when (not (string-empty-p buttons-text))
        (insert (propertize buttons-text
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))))

;; Function to handle button clicks
(defun ai-auto-complete-message-actions-button-click (event)
  "Handle a click on a message action button at EVENT."
  (interactive "e")
  (let* ((pos (if (mouse-event-p event)
                 (posn-point (event-end event))
               (point)))
         (action (get-text-property pos 'ai-auto-complete-message-action))
         (role (get-text-property pos 'ai-auto-complete-message-role))
         (message (get-text-property pos 'ai-auto-complete-message-content))
         (index (get-text-property pos 'ai-auto-complete-message-index)))

    (cond
     ;; Edit action
     ((eq action 'edit)
      (ai-auto-complete-message-actions-edit role message index))

     ;; Regenerate action
     ((eq action 'regenerate)
      (ai-auto-complete-message-actions-regenerate role message index))

     ;; Copy action
     ((eq action 'copy)
      (ai-auto-complete-message-actions-copy role message index))

     ;; Delete action
     ((eq action 'delete)
      (ai-auto-complete-message-actions-delete role message index)))))

;; Function to edit a message
(defun ai-auto-complete-message-actions-edit (role message index)
  "Edit a message with ROLE and MESSAGE at INDEX."
  (when (and (boundp 'ai-auto-complete--chat-history)
             (>= index 0)
             (< index (length ai-auto-complete--chat-history)))

    ;; Check if we're already editing a message
    (when ai-auto-complete-message-actions-editing
      (ai-auto-complete-message-actions-cancel-edit))

    ;; Find the message in the buffer
    (with-current-buffer (ai-auto-complete-message-actions-get-chat-buffer)
      (let ((inhibit-read-only t)
            (found nil))

        ;; Search for the message
        (save-excursion
          (goto-char (point-min))
          (while (and (not found)
                      (search-forward message nil t))
            (let ((msg-role (get-text-property (point) 'ai-auto-complete-message-role))
                  (msg-index (get-text-property (point) 'ai-auto-complete-message-index)))
              (when (and (eq msg-role role)
                         (eq msg-index index))
                (setq found t)

                ;; Save the original message and role
                (setq ai-auto-complete-message-actions-original-message message
                      ai-auto-complete-message-actions-edit-role role
                      ai-auto-complete-message-actions-edit-index index
                      ai-auto-complete-message-actions-editing t)

                ;; Replace the message with an editable version
                (let ((start (search-backward message nil t))
                      (end (+ (point) (length message))))
                  (delete-region start end)
                  (goto-char start)
                  (insert (propertize message
                                     'face 'ai-auto-complete-message-actions-edit-face
                                     'read-only nil
                                     'rear-nonsticky t))

                  ;; Set the edit marker
                  (setq ai-auto-complete-message-actions-edit-marker (point-marker))

                  ;; Add save/cancel buttons
                  (insert (propertize "\n\n"
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (insert (propertize " [Save] "
                                     'face 'ai-auto-complete-message-actions-button-face
                                     'mouse-face 'highlight
                                     'help-echo "Save changes"
                                     'ai-auto-complete-message-action 'save
                                     'button t
                                     'follow-link t
                                     'keymap (let ((map (make-sparse-keymap)))
                                              (define-key map [mouse-1] 'ai-auto-complete-message-actions-save-edit)
                                              map)))
                  (insert (propertize " [Cancel] "
                                     'face 'ai-auto-complete-message-actions-button-face
                                     'mouse-face 'highlight
                                     'help-echo "Cancel editing"
                                     'ai-auto-complete-message-action 'cancel
                                     'button t
                                     'follow-link t
                                     'keymap (let ((map (make-sparse-keymap)))
                                              (define-key map [mouse-1] 'ai-auto-complete-message-actions-cancel-edit)
                                              map))))))))))))

;; Function to save an edited message
(defun ai-auto-complete-message-actions-save-edit (event)
  "Save changes to an edited message at EVENT."
  (interactive "e")
  (when ai-auto-complete-message-actions-editing
    (with-current-buffer (ai-auto-complete-message-actions-get-chat-buffer)
      (let* ((inhibit-read-only t)
             (edited-message (buffer-substring-no-properties
                             ai-auto-complete-message-actions-edit-marker
                             (save-excursion
                               (goto-char ai-auto-complete-message-actions-edit-marker)
                               (search-forward "\n\n" nil t)
                               (- (point) 2))))
             (role ai-auto-complete-message-actions-edit-role)
             (index ai-auto-complete-message-actions-edit-index))

        ;; Update the message in the history
        (when (and (boundp 'ai-auto-complete--chat-history)
                   (>= index 0)
                   (< index (length ai-auto-complete--chat-history)))
          (let ((entry (nth index ai-auto-complete--chat-history)))
            (cond
             ((eq role 'user)
              (setf (cdr entry) edited-message))
             ((eq role 'assistant)
              (setf (cdr entry) edited-message))
             ((eq role 'agent)
              (setf (cddr entry) edited-message)))))

        ;; Reset editing state
        (setq ai-auto-complete-message-actions-editing nil
              ai-auto-complete-message-actions-original-message nil
              ai-auto-complete-message-actions-edit-role nil
              ai-auto-complete-message-actions-edit-index nil)

        ;; Clear the edit marker
        (when ai-auto-complete-message-actions-edit-marker
          (set-marker ai-auto-complete-message-actions-edit-marker nil)
          (setq ai-auto-complete-message-actions-edit-marker nil))

        ;; Refresh the display
        (when (fboundp 'ai-auto-complete-enhanced-chat-refresh)
          (ai-auto-complete-enhanced-chat-refresh))

        (message "Message updated")))))

;; Function to cancel editing
(defun ai-auto-complete-message-actions-cancel-edit (event)
  "Cancel editing a message at EVENT."
  (interactive "e")
  (when ai-auto-complete-message-actions-editing
    ;; Reset editing state
    (setq ai-auto-complete-message-actions-editing nil
          ai-auto-complete-message-actions-original-message nil
          ai-auto-complete-message-actions-edit-role nil
          ai-auto-complete-message-actions-edit-index nil)

    ;; Clear the edit marker
    (when ai-auto-complete-message-actions-edit-marker
      (set-marker ai-auto-complete-message-actions-edit-marker nil)
      (setq ai-auto-complete-message-actions-edit-marker nil))

    ;; Refresh the display
    (when (fboundp 'ai-auto-complete-enhanced-chat-refresh)
      (ai-auto-complete-enhanced-chat-refresh))

    (message "Editing canceled")))

;; Function to regenerate a message
(defun ai-auto-complete-message-actions-regenerate (role message index &optional agent-name)
  "Regenerate an assistant's message at INDEX.
ROLE and MESSAGE are from the original assistant message being regenerated.
Optional AGENT-NAME can specify the agent context for regeneration."
  (when (and (boundp 'ai-auto-complete--chat-history)
             (>= index 0)
             (< index (length ai-auto-complete--chat-history))
             (eq role 'assistant))
    (message "Regenerating response...")
    ;; Truncate history: Remove the assistant message at INDEX and all subsequent messages.
    ;; The history now ends with the message that prompted the assistant's original response.
    (let ((history-length (length ai-auto-complete--chat-history)))
      (setq ai-auto-complete--chat-history
            (butlast ai-auto-complete--chat-history (- history-length index))))

    ;; Refresh the display
    (when (fboundp 'ai-auto-complete-enhanced-chat-refresh)
      (ai-auto-complete-enhanced-chat-refresh))

    ;; Prepare arguments for ai-auto-complete-complete
    (let* ((agent-name-to-use (or agent-name ai-auto-complete-default-agent))
           ;; current-history is the truncated history.
           (current-history ai-auto-complete--chat-history)
           ;; context-message-pair is the last message in the truncated history
           ;; (e.g., the user message that prompted the original assistant response).
           (context-message-pair (when current-history (car (last current-history))))
           ;; prompt-text (the 'context' for ai-auto-complete-complete) is the content of that last message.
           (prompt-text (if context-message-pair
                            (let ((msg-role (car context-message-pair))
                                  (msg-content (cdr context-message-pair)))
                              ;; Handle different message structures (user vs agent)
                              (if (and (consp msg-content) (stringp (car msg-content))) ; Agent message like (agent "name" . "content")
                                  (cdr msg-content)
                                msg-content)) ; User message like (user . "content")
                          "")) ; Default to empty if history was cleared
           ;; history-for-completion is the history *before* the context message.
           (history-for-completion (if current-history
                                       (butlast current-history 1)
                                     nil))
           ;; Determine the base backend; ai-auto-complete-complete will refine this based on agent.
           (backend-for-completion (if (fboundp 'ai-auto-complete-get-current-backend)
                                       (ai-auto-complete-get-current-backend)
                                     ai-auto-complete-backend)) ; Fallback to global default
           (callback-fn (lambda (new-response)
                          ;; Stream the new response as 'assistant', using the determined agent context.
                          (ai-auto-complete-streaming-simulate new-response 'assistant agent-name-to-use))))

      (message "DEBUG: Regenerating with agent: %s, base backend: %s" agent-name-to-use backend-for-completion)
      (message "DEBUG: Context for completion (prompt-text): %s" (substring prompt-text 0 (min (length prompt-text) 100)))
      (message "DEBUG: History for completion has %d messages" (length history-for-completion))

      (ai-auto-complete-complete backend-for-completion
                                 prompt-text
                                 history-for-completion
                                 callback-fn
                                 agent-name-to-use))))

;; Function to copy a message
(defun ai-auto-complete-message-actions-copy (role message index)
  "Copy a message with ROLE and MESSAGE at INDEX to the kill ring."
  (kill-new message)
  (message "Message copied to clipboard"))

;; Function to delete a message
(defun ai-auto-complete-message-actions-delete (role message index)
  "Delete a message with ROLE and MESSAGE at INDEX."
  (when (and (boundp 'ai-auto-complete--chat-history)
             (>= index 0)
             (< index (length ai-auto-complete--chat-history)))

    ;; Confirm deletion
    (when (yes-or-no-p "Delete this message? ")
      ;; Remove the message from history
      (setq ai-auto-complete--chat-history
            (append (butlast ai-auto-complete--chat-history (- (length ai-auto-complete--chat-history) index))
                    (nthcdr (1+ index) ai-auto-complete--chat-history)))

      ;; Refresh the display
      (when (fboundp 'ai-auto-complete-enhanced-chat-refresh)
        (ai-auto-complete-enhanced-chat-refresh))

      (message "Message deleted"))))

(provide 'ui/message-actions)
(message "message-actions.el loaded")
;;; message-actions.el ends here
