streaming.el is responsible for streaming content from the LLM backends and update the chat interface in enhacned-chat-mode. But 
the way it works presently, it is streaming characters in chunk but it's not doing smartly. what it is doing is it's streams, say, the first 100 charactes of content, then it stremas the first 200 and then the first 300 and so on. This is not a good user experience. The characters that have already been streamed, don't have to be re-streamed.Investigate this thoroughly and fix it.