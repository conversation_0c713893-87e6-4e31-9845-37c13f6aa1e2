;;; streaming.el --- Streaming support for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides streaming support for the chat interface.
;; It allows for real-time updates of responses and tool calls.

;;; Code:

(require 'cl-lib)
(require 'ui/markdown-renderer)
(require 'chat-customization) ; For buffer names

;; Customization options for streaming
(defgroup ai-auto-complete-streaming nil
  "Settings for streaming in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-streaming-")

(defcustom ai-auto-complete-streaming-enabled t
  "Enable streaming for responses and tool calls."
  :type 'boolean
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-markdown-enabled nil
  "Enable markdown rendering mode.
When enabled:
- Streaming is automatically disabled
- Content is rendered from history with markdown formatting
- New messages trigger a full re-render from history
When disabled:
- Streaming is enabled for real-time display
- Content is displayed as plain text"
  :type 'boolean
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-update-interval 0.1
  "Interval in seconds for updating the streaming display."
  :type 'float
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-typing-speed 30
  "Simulated typing speed in characters per second for non-streaming backends."
  :type 'integer
  :group 'ai-auto-complete-streaming)

;; Variables for tracking streaming state
(defvar-local ai-auto-complete-streaming-in-progress nil
  "Whether streaming is currently in progress.")

(defvar-local ai-auto-complete-streaming-timer nil
  "Timer for updating the streaming display.")

(defvar-local ai-auto-complete-streaming-buffer ""
  "Buffer for accumulating streaming content.")

(defvar-local ai-auto-complete-streaming-position nil
  "Position marker for the current streaming insertion point.")

(defvar-local ai-auto-complete-streaming-display-end nil
  "Position marker for the end of currently displayed streaming content.")

(defvar-local ai-auto-complete-streaming-role nil
  "Role of the current streaming message (user, assistant, agent, etc.).")

(defvar-local ai-auto-complete-streaming-agent nil
  "Agent name for the current streaming message, if applicable.")

(defvar-local ai-auto-complete-streaming-tool-calls nil
  "List of tool calls detected during streaming.")

(defvar-local ai-auto-complete-streaming-tool-results nil
  "List of tool results received during streaming.")

(defvar-local ai-auto-complete-streaming-timestamps nil
  "Alist of timestamps for streaming events.")

;; Functions for streaming support

(defun ai-auto-complete-streaming-get-chat-buffer ()
  "Get the appropriate chat buffer for streaming.
Returns the enhanced chat buffer if it exists and enhanced chat mode is active,
otherwise returns the regular chat buffer."
  (let* ((enhanced-chat-buffer (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
         (use-enhanced-chat-buffer
          (and enhanced-chat-buffer
               (buffer-live-p enhanced-chat-buffer)
               (with-current-buffer enhanced-chat-buffer
                 (and (boundp 'ai-auto-complete-enhanced-chat-mode)
                      ai-auto-complete-enhanced-chat-mode)))))
    (if use-enhanced-chat-buffer
        enhanced-chat-buffer
      (get-buffer-create ai-auto-complete-chat-buffer-name))))

(defun ai-auto-complete-streaming-start (role &optional agent-name)
  "Start streaming a message with ROLE.
If ROLE is 'agent, AGENT-NAME should be provided."
  (message "[DEBUG streaming-start] Called with role: %s, agent-name: %s" role (or agent-name "nil"))
  (message "[DEBUG streaming-start] Current buffer: %s" (current-buffer))
  (when ai-auto-complete-streaming-enabled
    (with-current-buffer (ai-auto-complete-streaming-get-chat-buffer)
      ;; Cancel any existing streaming timer
      (when (and ai-auto-complete-streaming-timer
                 (timerp ai-auto-complete-streaming-timer))
        (cancel-timer ai-auto-complete-streaming-timer)
        (setq ai-auto-complete-streaming-timer nil))

      ;; Set up streaming state
      (setq ai-auto-complete-streaming-in-progress t
            ai-auto-complete-streaming-buffer ""
            ai-auto-complete-streaming-role role
            ai-auto-complete-streaming-agent agent-name
            ai-auto-complete-streaming-tool-calls nil
            ai-auto-complete-streaming-tool-results nil
            ai-auto-complete-streaming-timestamps nil)

      ;; Add initial timestamp
      (push (cons 'start (current-time)) ai-auto-complete-streaming-timestamps)

      ;; Insert the message prefix based on role
      (let ((inhibit-read-only t))
        (goto-char (point-max))
        ;; If there's an input marker, delete from there to the end
        ;; Check which input marker to use based on the current mode
        (let ((input-marker (if (and (boundp 'ai-auto-complete-enhanced-chat-mode)
                                    ai-auto-complete-enhanced-chat-mode
                                    (boundp 'ai-auto-complete-enhanced-chat-input-marker))
                               ai-auto-complete-enhanced-chat-input-marker
                             ai-auto-complete--chat-input-marker)))
          (when (and input-marker (marker-position input-marker))
            (delete-region input-marker (point-max))))

        ;; Insert the appropriate prefix based on role
        (cond
         ((eq role 'user)
          (insert (propertize ai-auto-complete-chat-prompt-prefix
                             'face 'ai-auto-complete-user-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'assistant)
          (insert (propertize ai-auto-complete-chat-response-prefix
                             'face 'ai-auto-complete-assistant-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'agent)
          (insert (propertize (format "AGENT-%s: " agent-name)
                             'face 'ai-auto-complete-agent-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'tool)
          (insert (propertize "TOOL: "
                             'face 'ai-auto-complete-tool-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'tool-result)
          (insert (propertize "RESULT: "
                             'face 'ai-auto-complete-tool-result-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'emacs)
          (insert (propertize "emacs: "
                             'face 'ai-auto-complete-system-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))))

        ;; Set the streaming position markers
        (setq ai-auto-complete-streaming-position (point-marker)
              ai-auto-complete-streaming-display-end (point-marker))))))

(defun ai-auto-complete-streaming-update (chunk)
  "Update the streaming display with CHUNK of text.
CHUNK can be either:
- An incremental chunk of new text (from native streaming)
- The full accumulated text so far (from simulated streaming)

The function automatically detects which case it is and handles it appropriately."
  (when (and ai-auto-complete-streaming-enabled ai-auto-complete-streaming-in-progress)
    (with-current-buffer (ai-auto-complete-streaming-get-chat-buffer)
      (let ((inhibit-read-only t)
            (old-buffer-length (length ai-auto-complete-streaming-buffer)))

        ;; Detect if this is an incremental chunk or full accumulated text
        (if (and (> old-buffer-length 0)
                 (string-prefix-p ai-auto-complete-streaming-buffer chunk))
            ;; This looks like accumulated text (chunk starts with our current buffer)
            ;; Extract only the new part
            (let ((new-text (substring chunk old-buffer-length)))
              (when (> (length new-text) 0)
                ;; Update our buffer with the full accumulated text
                (setq ai-auto-complete-streaming-buffer chunk)

                ;; Insert only the new text at the display end position
                (save-excursion
                  (goto-char ai-auto-complete-streaming-display-end)
                  (insert (propertize new-text
                                     'read-only t
                                     'front-sticky t
                                     'rear-nonsticky t))
                  (setq ai-auto-complete-streaming-display-end (point-marker)))))

          ;; This is an incremental chunk - append it
          (when (> (length chunk) 0)
            ;; Append the chunk to our buffer
            (setq ai-auto-complete-streaming-buffer
                  (concat ai-auto-complete-streaming-buffer chunk))

            ;; Insert the chunk at the display end position
            (save-excursion
              (goto-char ai-auto-complete-streaming-display-end)
              (insert (propertize chunk
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (setq ai-auto-complete-streaming-display-end (point-marker))))))

      ;; Check for tool calls in the updated buffer
      (ai-auto-complete-streaming-check-for-tool-calls))))

(defun ai-auto-complete-streaming-check-for-tool-calls ()
  "Check for tool calls in the streaming buffer and execute them."
  (when (and ai-auto-complete-streaming-enabled
             ai-auto-complete-streaming-in-progress
             (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled)
    ;; Look for tool call patterns in the buffer
    (let ((buffer ai-auto-complete-streaming-buffer)
          (tool-calls nil))
      ;; Simple pattern matching for tool calls
      (with-temp-buffer
        (insert buffer)
        (goto-char (point-min))
        (while (re-search-forward "<tool name=\"\\([^\"]+\\)\">" nil t)
          (let ((tool-name (match-string 1))
                (start-pos (match-beginning 0)))
            ;; Try to find the closing tag
            (when (re-search-forward "</tool>" nil t)
              (let ((end-pos (match-end 0))
                    (tool-text (buffer-substring-no-properties start-pos (match-end 0))))
                ;; Extract parameters
                (goto-char start-pos)
                (when (re-search-forward "<parameters>\\(.*?\\)</parameters>" end-pos t)
                  (let ((params-text (match-string 1)))
                    ;; Try to parse parameters as JSON
                    (condition-case nil
                        (let ((params (json-read-from-string params-text)))
                          ;; Add to tool calls if not already processed
                          (unless (member (cons tool-name params) ai-auto-complete-streaming-tool-calls)
                            (push (cons tool-name params) tool-calls)
                            (push (cons tool-name params) ai-auto-complete-streaming-tool-calls)))
                      (error nil))))))))

      ;; Execute any new tool calls
      (dolist (tool-call (nreverse tool-calls))
        (let ((tool-name (car tool-call))
              (params (cdr tool-call)))
          ;; Add timestamp for tool call
          (push (cons (intern (format "tool-call-%s" tool-name)) (current-time))
                ai-auto-complete-streaming-timestamps)

          ;; Display the tool call
          (ai-auto-complete-streaming-display-tool-call tool-name params)

          ;; Execute the tool if it exists
          (when (and (boundp 'ai-auto-complete-tools)
                     (hash-table-p ai-auto-complete-tools)
                     (gethash tool-name ai-auto-complete-tools))
            (let ((tool (gethash tool-name ai-auto-complete-tools))
                  (tool-fn (gethash tool-name ai-auto-complete-tools)))
              (when (and tool (plist-get tool :function))
                (let ((fn (plist-get tool :function)))
                  (funcall fn params
                           (lambda (result)
                             ;; Add timestamp for tool result
                             (push (cons (intern (format "tool-result-%s" tool-name)) (current-time))
                                   ai-auto-complete-streaming-timestamps)

                             ;; Display the tool result
                             (ai-auto-complete-streaming-display-tool-result tool-name result)

                             ;; Add to tool results
                             (push (cons tool-name result) ai-auto-complete-streaming-tool-results)))))))))))))

(defun ai-auto-complete-streaming-display-tool-call (tool-name params)
  "Display a tool call in the chat buffer.
TOOL-NAME is the name of the tool being called.
PARAMS is a plist, alist, or hash-table of arguments for the tool."
  (with-current-buffer (ai-auto-complete-streaming-get-chat-buffer)
    (let ((inhibit-read-only t)
          (args-str ""))
      ;; Format the arguments based on the type of params
      (cond
       ;; Hash table
       ((hash-table-p params)
        (maphash (lambda (key value)
                   (setq args-str (concat args-str
                                         (format "\n  %s: %s" key value))))
                 params))
       ;; Association list (alist)
       ((and (listp params) (consp (car-safe params)))
        (dolist (pair params)
          (setq args-str (concat args-str
                                (format "\n  %s: %s" (car pair) (cdr pair))))))
       ;; Property list (plist)
       ((listp params)
        (let ((key-values params))
          (while key-values
            (when (and (car key-values) (cadr key-values))
              (setq args-str (concat args-str
                                    (format "\n  %s: %s" (car key-values) (cadr key-values)))))
            (setq key-values (cddr key-values)))))
       ;; Other types (just convert to string)
       (t
        (setq args-str (concat args-str (format "\n  %s" params)))))

      ;; Display the tool call
      (save-excursion
        (goto-char (point-max))
        (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
        (insert (propertize "TOOL CALL: "
                           'face 'ai-auto-complete-tool-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize (format "%s%s" tool-name args-str)
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))))

(defun ai-auto-complete-streaming-display-tool-result (tool-name result)
  "Display a tool result in the chat buffer.
TOOL-NAME is the name of the tool that was called.
RESULT is the result returned by the tool."
  (with-current-buffer (ai-auto-complete-streaming-get-chat-buffer)
    (let ((inhibit-read-only t)
          (line-count (with-temp-buffer
                        (insert result)
                        (count-lines (point-min) (point-max))))
          (make-collapsible (and (fboundp 'ai-auto-complete-collapsible-create)
                                (> line-count ai-auto-complete-collapsible-max-lines))))
      ;; Display the tool result
      (save-excursion
        (goto-char (point-max))
        (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
        (insert (propertize "TOOL RESULT: "
                           'face 'ai-auto-complete-tool-result-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))

        ;; Format the result
        (let ((formatted-result (format "%s\n%s" tool-name result)))
          ;; Make it collapsible if needed
          (if make-collapsible
              (insert (ai-auto-complete-collapsible-create
                      (format "Tool Result: %s (%d lines)" tool-name line-count)
                      formatted-result
                      'tool-result
                      'expanded))
            ;; Otherwise just insert the formatted result
            (insert (propertize formatted-result
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))))))))

(defun ai-auto-complete-streaming-complete ()
  "Complete the streaming process and finalize the message."
  (message "[DEBUG streaming-complete] Called")
  (message "[DEBUG streaming-complete] Current buffer: %s" (current-buffer))
  (message "[DEBUG streaming-complete] Streaming enabled: %s, in progress: %s"
           ai-auto-complete-streaming-enabled ai-auto-complete-streaming-in-progress)
  (when (and ai-auto-complete-streaming-enabled ai-auto-complete-streaming-in-progress)
    (with-current-buffer (ai-auto-complete-streaming-get-chat-buffer)
      ;; Add completion timestamp
      (push (cons 'complete (current-time)) ai-auto-complete-streaming-timestamps)

      ;; Cancel any existing streaming timer
      (when (and ai-auto-complete-streaming-timer
                 (timerp ai-auto-complete-streaming-timer))
        (cancel-timer ai-auto-complete-streaming-timer)
        (setq ai-auto-complete-streaming-timer nil))

      ;; If markdown mode is enabled, trigger a full re-render from history
      ;; instead of applying markdown to just this message
      (when ai-auto-complete-streaming-markdown-enabled
        (message "[DEBUG streaming-complete] Markdown mode enabled - triggering full re-render from history...")
        (ai-auto-complete-streaming-rerender-from-history t)
        (message "[DEBUG streaming-complete] Full re-render complete"))

      ;; Add the message to history
      (when (boundp 'ai-auto-complete--chat-history)
        (message "[DEBUG streaming-complete] Adding message to history with role: %s" ai-auto-complete-streaming-role)
        (cond
         ((eq ai-auto-complete-streaming-role 'user)
          (push (cons 'user ai-auto-complete-streaming-buffer) ai-auto-complete--chat-history)
          (message "[DEBUG streaming-complete] Added user message to history"))
         ((eq ai-auto-complete-streaming-role 'assistant)
          (push (cons 'assistant ai-auto-complete-streaming-buffer) ai-auto-complete--chat-history)
          (message "[DEBUG streaming-complete] Added assistant message to history"))
         ((eq ai-auto-complete-streaming-role 'agent)
          (push (cons 'agent (cons ai-auto-complete-streaming-agent ai-auto-complete-streaming-buffer))
                ai-auto-complete--chat-history)
          (message "[DEBUG streaming-complete] Added agent message to history for agent: %s" ai-auto-complete-streaming-agent)))
        (message "[DEBUG streaming-complete] History now has %d messages" (length ai-auto-complete--chat-history)))

      ;; Add tool calls and results to history if any
      (when (and (boundp 'ai-auto-complete--chat-history)
                 ai-auto-complete-streaming-tool-calls)
        (let ((tool-history-entry
               (format "Tool calls executed:\n%s\n\n%s"
                       (mapconcat (lambda (tool-call)
                                    (format "- %s: %s"
                                            (car tool-call)
                                            (json-encode (cdr tool-call))))
                                  ai-auto-complete-streaming-tool-calls
                                  "\n")
                       (if ai-auto-complete-streaming-tool-results
                           "All tool calls completed successfully."
                         "No tool results received."))))
          (push (cons 'tool-result tool-history-entry) ai-auto-complete--chat-history)))

      ;; Reset streaming state
      (setq ai-auto-complete-streaming-in-progress nil
            ai-auto-complete-streaming-buffer ""
            ai-auto-complete-streaming-position nil
            ai-auto-complete-streaming-display-end nil
            ai-auto-complete-streaming-role nil
            ai-auto-complete-streaming-agent nil
            ai-auto-complete-streaming-tool-calls nil
            ai-auto-complete-streaming-tool-results nil)

      ;; Restore the input marker - check which mode we're in
      (goto-char (point-max))
      (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
      (insert (propertize ai-auto-complete-chat-prompt-prefix
                         'face 'ai-auto-complete-user-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
      ;; Set the appropriate input marker based on the current mode
      (if (and (boundp 'ai-auto-complete-enhanced-chat-mode)
               ai-auto-complete-enhanced-chat-mode)
          (setq ai-auto-complete-enhanced-chat-input-marker (point-marker))
        (setq ai-auto-complete--chat-input-marker (point-marker)))
      (put-text-property (point) (point) 'read-only nil))))

(defcustom ai-auto-complete-streaming-min-chunk-size 100
  "Minimum number of characters to accumulate before updating the display.
Larger values will result in less frequent updates but may feel less responsive."
  :type 'integer
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-max-update-frequency 0.2
  "Maximum frequency of streaming updates in seconds.
Lower values will update more frequently but may cause performance issues."
  :type 'float
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-smart-mode t
  "Whether to use smart streaming mode.
When enabled, streaming will adapt based on response size and speed."
  :type 'boolean
  :group 'ai-auto-complete-streaming)

(defvar ai-auto-complete-streaming-last-update-time nil
  "Timestamp of the last streaming update.")

(defvar ai-auto-complete-streaming-accumulated-text ""
  "Text accumulated since the last streaming update.")

(defun ai-auto-complete-streaming-simulate (text &optional role agent-name)
  "Simulate streaming for TEXT with ROLE and optional AGENT-NAME.
This is used for backends that don't support native streaming."
  (message "[DEBUG streaming-simulate] Called with role: %s, agent-name: %s, text length: %d"
           (or role 'assistant) (or agent-name "nil") (length text))
  (message "[DEBUG streaming-simulate] Text preview (first 100 chars): %s"
           (if (> (length text) 100) (concat (substring text 0 100) "...") text))
  (message "[DEBUG streaming-simulate] Current buffer: %s" (current-buffer))
  (when ai-auto-complete-streaming-enabled
    (message "[DEBUG streaming-simulate] Streaming enabled, starting...")
    ;; Start streaming
    (ai-auto-complete-streaming-start (or role 'assistant) agent-name)

    ;; Reset streaming variables
    (setq ai-auto-complete-streaming-last-update-time nil)
    (setq ai-auto-complete-streaming-accumulated-text "")

    ;; Determine if we should use smart streaming based on text length
    (let* ((use-smart-streaming (and ai-auto-complete-streaming-smart-mode
                                    (> (length text) (* 5 ai-auto-complete-streaming-min-chunk-size))))
           (text-length (length text))
           (is-short-response (< text-length 500))
           (is-code-block (string-match-p "```" text))
           (update-interval (cond
                             ;; For very short responses, just display immediately
                             (is-short-response 0)
                             ;; For code blocks, use slightly faster updates
                             (is-code-block 0.1)
                             ;; Otherwise use the configured interval
                             (t ai-auto-complete-streaming-max-update-frequency)))
           (chunk-size (cond
                        ;; For short responses, use the entire text
                        (is-short-response text-length)
                        ;; For code blocks, use larger chunks
                        (is-code-block (* 2 ai-auto-complete-streaming-min-chunk-size))
                        ;; Otherwise use the configured chunk size
                        (t ai-auto-complete-streaming-min-chunk-size)))
           (chunks (ai-auto-complete-streaming-split-text text chunk-size))
           (index 0))

      ;; If it's a very short response or smart streaming is disabled, just display it all at once
      (if (or is-short-response (not use-smart-streaming))
          (progn
            (message "[DEBUG streaming-simulate] Using immediate display (short response or smart streaming disabled)")
            (ai-auto-complete-streaming-update text)
            (ai-auto-complete-streaming-complete))

        ;; Otherwise, set up a timer for smart streaming
        (message "[DEBUG streaming-simulate] Using timer-based streaming with %d chunks" (length chunks))
        (setq ai-auto-complete-streaming-timer
              (run-with-timer 0 update-interval
                             (lambda ()
                               (if (< index (length chunks))
                                   (let ((current-time (current-time))
                                         (chunk (nth index chunks)))
                                     ;; Accumulate text
                                     (setq ai-auto-complete-streaming-accumulated-text
                                           (concat ai-auto-complete-streaming-accumulated-text chunk))

                                     ;; Update if enough time has passed or we've accumulated enough text
                                     (when (or (not ai-auto-complete-streaming-last-update-time)
                                              (> (length ai-auto-complete-streaming-accumulated-text)
                                                 ai-auto-complete-streaming-min-chunk-size)
                                              (> (float-time (time-subtract current-time
                                                                           ai-auto-complete-streaming-last-update-time))
                                                 ai-auto-complete-streaming-max-update-frequency))
                                       ;; Send only the accumulated text as an incremental chunk
                                       (ai-auto-complete-streaming-update ai-auto-complete-streaming-accumulated-text)
                                       (setq ai-auto-complete-streaming-accumulated-text "")
                                       (setq ai-auto-complete-streaming-last-update-time current-time))

                                     (setq index (1+ index)))
                                 ;; All chunks processed, complete streaming
                                 (progn
                                   (message "[DEBUG streaming-simulate] Timer completed all chunks, finishing...")
                                   ;; Update any remaining accumulated text
                                   (when (not (string-empty-p ai-auto-complete-streaming-accumulated-text))
                                     (ai-auto-complete-streaming-update ai-auto-complete-streaming-accumulated-text))

                                   (ai-auto-complete-streaming-complete)
                                   ;; Safely cancel the timer if it exists
                                   (when (and ai-auto-complete-streaming-timer
                                             (timerp ai-auto-complete-streaming-timer))
                                     (cancel-timer ai-auto-complete-streaming-timer))
                                   (setq ai-auto-complete-streaming-timer nil))))))))))

(defun ai-auto-complete-streaming-split-text (text chunk-size)
  "Split TEXT into chunks of CHUNK-SIZE characters."
  (let ((chunks '())
        (start 0)
        (len (length text)))
    (while (< start len)
      (push (substring text start (min len (+ start chunk-size))) chunks)
      (setq start (+ start chunk-size)))
    (nreverse chunks)))

(defun ai-auto-complete-streaming-rerender-from-history (use-markdown)
  "Re-render the entire conversation from history.
USE-MARKDOWN determines whether to apply markdown formatting."
  (message "[DEBUG rerender-from-history] Called with use-markdown: %s" use-markdown)
  (when (and (boundp 'ai-auto-complete-enhanced-chat-mode)
             ai-auto-complete-enhanced-chat-mode
             (boundp 'ai-auto-complete--chat-history)
             ai-auto-complete--chat-history)
    (let ((inhibit-read-only t))
      (message "[DEBUG rerender-from-history] Clearing content area and re-rendering %d messages"
               (length ai-auto-complete--chat-history))

      ;; Clear the content area (preserve header and input area)
      (when (and (boundp 'ai-auto-complete-enhanced-chat-content-marker)
                 (marker-position ai-auto-complete-enhanced-chat-content-marker)
                 (boundp 'ai-auto-complete-enhanced-chat-input-marker)
                 (marker-position ai-auto-complete-enhanced-chat-input-marker))
        (delete-region ai-auto-complete-enhanced-chat-content-marker
                      ai-auto-complete-enhanced-chat-input-marker))

      ;; Re-render each message from history (in reverse order since history is newest-first)
      (save-excursion
        (goto-char ai-auto-complete-enhanced-chat-content-marker)
        (let ((index 0))
          (dolist (msg (reverse ai-auto-complete--chat-history))
          (let ((role (car msg))
                (content (cdr msg)))
            (cond
             ((eq role 'user)
              (insert (propertize "USER: "
                                 'face 'ai-auto-complete-user-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (if use-markdown
                                     (ai-auto-complete-markdown-render content)
                                   content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-index index))
              )

             ((eq role 'assistant)
              (insert (propertize "AI: "
                                 'face 'ai-auto-complete-assistant-face
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))
              (insert (propertize (if use-markdown
                                     (ai-auto-complete-markdown-render content)
                                   content)
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t
                                 'ai-auto-complete-message-role role
                                 'ai-auto-complete-message-index index))
              )

             ((eq role 'agent)
              (let ((agent-name (car content))
                    (agent-content (cdr content)))
                (insert (propertize (format "AGENT-%s: " agent-name)
                                   'face 'ai-auto-complete-enhanced-chat-agent-face
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t))
                (insert (propertize (if use-markdown
                                       (ai-auto-complete-markdown-render agent-content)
                                     agent-content)
                                   'read-only t
                                   'front-sticky t
                                   'rear-nonsticky t
                                   'ai-auto-complete-message-role role
                                   'ai-auto-complete-message-index index)))))

            ;; Add message actions if enabled
            (when (and (fboundp 'ai-auto-complete-message-actions-insert-buttons)
                      (boundp 'ai-auto-complete-message-actions-enabled)
                      ai-auto-complete-message-actions-enabled
                      (memq role '(user assistant agent)))
              (let ((msg-content (if (eq role 'agent) (cdr content) content)))
                (ai-auto-complete-message-actions-insert-buttons role msg-content index)))

            ;; Add a newline after each message
            (insert (propertize "\n\n"
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))

            ;; Increment the index
            (setq index (1+ index)))))

      (message "[DEBUG rerender-from-history] Re-rendering complete")))))

;; Toggle function for markdown rendering
(defun ai-auto-complete-streaming-toggle-markdown ()
  "Toggle markdown rendering for streamed content.
When enabling markdown mode:
- Streaming is disabled (since we re-render from history)
- Buffer is cleared and conversation is re-rendered from history with markdown
When disabling markdown mode:
- Streaming is re-enabled
- Buffer is cleared and conversation is re-rendered from history as plain text"
  (interactive)
  (setq ai-auto-complete-streaming-markdown-enabled
        (not ai-auto-complete-streaming-markdown-enabled))

  (if ai-auto-complete-streaming-markdown-enabled
      (progn
        ;; Enabling markdown mode
        (message "Markdown rendering enabled - streaming disabled, re-rendering from history...")
        ;; Disable streaming when markdown is enabled
        (setq ai-auto-complete-streaming-enabled nil)
        ;; Clear buffer and re-render everything from history with markdown
        (ai-auto-complete-streaming-rerender-from-history t))
    (progn
      ;; Disabling markdown mode
      (message "Markdown rendering disabled - streaming enabled, re-rendering from history...")
      ;; Re-enable streaming when markdown is disabled
      (setq ai-auto-complete-streaming-enabled t)
      ;; Clear buffer and re-render everything from history without markdown
      (ai-auto-complete-streaming-rerender-from-history nil)))

  ;; Refresh the enhanced chat interface if it's active
  (when (and (boundp 'ai-auto-complete-enhanced-chat-mode)
             ai-auto-complete-enhanced-chat-mode
             (fboundp 'ai-auto-complete-enhanced-chat-refresh))
    (ai-auto-complete-enhanced-chat-refresh)))

(provide 'ui/streaming)
;;; streaming.el ends here
