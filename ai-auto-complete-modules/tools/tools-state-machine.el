;;; tools-state-machine.el --- State machine for tools in ai-auto-complete -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a state machine implementation for tools in the AI Auto Complete package.
;; It replaces the deeply nested callback structure with a flatter, more maintainable approach.

;;; Code:

(require 'cl-lib)

;; Define a state object to track tool processing
(cl-defstruct (ai-auto-complete-tool-state (:constructor ai-auto-complete-tool-state-create)
                                          (:copier nil))
  "State object for tool processing."
  (response nil :read-only nil :documentation "Current response being processed.")
  (tool-calls nil :read-only nil :documentation "List of tool calls to process.")
  (current-tool-index 0 :read-only nil :documentation "Index of current tool being processed.")
  (tool-results "" :read-only nil :documentation "Accumulated tool results.")
  (success-count 0 :read-only nil :documentation "Number of successful tool calls.")
  (error-count 0 :read-only nil :documentation "Number of failed tool calls.")
  (depth 0 :read-only nil :documentation "Current recursion depth.")
  (callback nil :read-only nil :documentation "Final callback to call with result.")
  (backend nil :read-only nil :documentation "Backend to use for LLM calls.")
  (original-response nil :read-only nil :documentation "Original response with tool calls.")
  (agent-name nil :read-only nil :documentation "Name of the agent making the request.")
  (history nil :read-only nil :documentation "Conversation history for context."))

;; Trampoline function to flatten recursion
(defun ai-auto-complete-tools-trampoline (initial-fn &rest initial-args)
  "Execute INITIAL-FN with INITIAL-ARGS, then any returned functions until done.
This flattens recursion into iteration, preventing excessive nesting."
  (let ((result (apply initial-fn initial-args)))
    (while (and result (listp result) (functionp (car result)))
      (setq result (apply (car result) (cdr result))))
    result))

;; Main entry point for tool processing
(defun ai-auto-complete-tools-process-with-state-machine (response callback &optional agent-name)
  "Process tool calls in RESPONSE using a state machine and call CALLBACK with result.
AGENT-NAME is the optional name of the agent making the request."
  (message "Processing response with state machine")
  (let ((effective-callback (if callback
                               callback
                             (lambda (resp)
                               (ai-auto-complete-tools-default-callback resp agent-name))))
        ;; Get the current conversation history
        (current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                   ai-auto-complete--chat-history)
                          ai-auto-complete--chat-history)))
    (cond
     ;; Check if tools are disabled
     ((not ai-auto-complete-tools-enabled)
      (message "Tools not enabled, returning original response")
      (funcall effective-callback response))

     ;; Check if the response is an error message
     ((or (string-match-p "^Error:" response) (string-match-p "^ERROR:" response))
      (message "Detected error response, not processing for tools: %s"
               (substring response 0 (min 100 (length response))))
      (funcall effective-callback response))

     ;; Check if the response is empty or nil
     ((or (null response) (string-empty-p (string-trim response)))
      (message "Empty response received, not processing for tools")
      (funcall effective-callback (or response "")))

     ;; Process normal response with the state machine
     (t
      (ai-auto-complete-tools-trampoline
       #'ai-auto-complete-tools-start-processing
       response effective-callback agent-name current-history)))))

;; Start processing a response
(defun ai-auto-complete-tools-start-processing (response callback &optional agent-name history)
  "Start processing RESPONSE for tool calls and call CALLBACK with result.
AGENT-NAME is the optional name of the agent making the request.
HISTORY is the conversation history to maintain context."
  (message "Starting tool processing")
  (message "ai-auto-complete-tools-start-processing called with history: %s"
           (if history (format "%d messages" (length history)) "nil"))

  ;; Check if the response is an error message
  (if (or (string-match-p "^Error:" response) (string-match-p "^ERROR:" response))
      (progn
        (message "Detected error response, not processing for tools: %s"
                 (substring response 0 (min 100 (length response))))
        (funcall callback response))

    ;; Check if there are any tool calls in the response
    (if (not (string-match-p "<tool name=" response))
        ;; No tool calls, just return the response
        (progn
          (message "No tool calls found, returning response")
          (funcall callback response))
      ;; Parse tool calls
      (let ((tool-calls (ai-auto-complete-tools-parse-response response)))
        (message "Found %d tool calls in response" (length tool-calls))
        (if (null tool-calls)
            ;; No tool calls found after parsing
            (progn
              (message "No tool calls found after parsing, returning response")
              (funcall callback response))
          ;; Create initial state and start processing
          (let* ((backend (if (and (boundp 'ai-auto-complete-chat-mode) ai-auto-complete-chat-mode)
                             ai-auto-complete-backend
                           (ai-auto-complete-get-current-backend)))
                 (state (ai-auto-complete-tool-state-create
                         :response response
                         :tool-calls tool-calls
                         :callback callback
                         :backend backend
                         :original-response response
                         :agent-name agent-name
                         :history history)))
            (list #'ai-auto-complete-tools-process-next-tool state)))))))

;; Process the next tool in the queue
(defun ai-auto-complete-tools-process-next-tool (state)
  "Process the next tool in STATE and return the next function to call."
  (let ((tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (current-index (ai-auto-complete-tool-state-current-tool-index state)))

    ;; Check if we've processed all tools
    (if (>= current-index (length tool-calls))
        ;; All tools processed, continue with LLM
        (list #'ai-auto-complete-tools-continue-with-llm state)

      ;; Process the current tool
      (let* ((tool-call (nth current-index tool-calls))
             (tool-name (car tool-call))
             (params (cdr tool-call))
             (tool (gethash tool-name ai-auto-complete-tools))
             (tool-fn (and tool (plist-get tool :function))))

        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Executing tool: %s" tool-name))

        ;; Execute the tool and update state
        (let ((result nil)
              (error-occurred nil)
              (error-msg nil))

          ;; Try to execute the tool
          (condition-case err
              (when tool-fn
                (setq result (funcall tool-fn params))
                (setf (ai-auto-complete-tool-state-success-count state)
                      (1+ (ai-auto-complete-tool-state-success-count state))))
            (error
             (setq error-occurred t)
             (setq error-msg (error-message-string err))
             (setf (ai-auto-complete-tool-state-error-count state)
                   (1+ (ai-auto-complete-tool-state-error-count state)))))

          ;; Handle the result or error
          (let ((formatted-result
                 (cond
                  (error-occurred
                   (let ((msg (format "ERROR executing tool %s: %s" tool-name error-msg)))
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[TOOLS-DEBUG] %s" msg))
                     (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                  ((not tool-fn)
                   (let ((msg (format "ERROR: Tool %s not found or has no function" tool-name)))
                     (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                       (message "[TOOLS-DEBUG] %s" msg))
                     (setf (ai-auto-complete-tool-state-error-count state)
                           (1+ (ai-auto-complete-tool-state-error-count state)))
                     (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name msg)))

                  (t
                   (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                     (message "[TOOLS-DEBUG] Tool %s executed successfully" tool-name))
                   (format "\n\n<tool_result name=\"%s\">\n%s\n</tool_result>" tool-name result)))))

            ;; Update state with the result
            (setf (ai-auto-complete-tool-state-tool-results state)
                  (concat (ai-auto-complete-tool-state-tool-results state) formatted-result))

            ;; Move to the next tool
            (setf (ai-auto-complete-tool-state-current-tool-index state) (1+ current-index))

            ;; Continue processing
            (list #'ai-auto-complete-tools-process-next-tool state)))))))

;; Continue the conversation with the LLM
(defun ai-auto-complete-tools-continue-with-llm (state)
  "Continue the conversation with the LLM using the results in STATE."
  (let ((tool-results (ai-auto-complete-tool-state-tool-results state))
        (success-count (ai-auto-complete-tool-state-success-count state))
        (error-count (ai-auto-complete-tool-state-error-count state))
        (tool-calls (ai-auto-complete-tool-state-tool-calls state))
        (original-response (ai-auto-complete-tool-state-original-response state))
        (callback (ai-auto-complete-tool-state-callback state))
        (backend (ai-auto-complete-tool-state-backend state))
        (depth (ai-auto-complete-tool-state-depth state))
        (agent-name (ai-auto-complete-tool-state-agent-name state)))

    ;; Log summary of tool execution
    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] Tool execution summary: %d successful, %d failed"
               success-count error-count))

    ;; Check if we've reached the maximum recursion depth
    (if (> depth 5)
        (progn
          (message "Maximum recursion depth reached, returning simplified response")
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat original-response
                                      "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat original-response
                    "\n\n[ERROR: Maximum tool recursion depth reached. Please continue without further tool calls.]")
             agent-name)))

      ;; Prepare the continuation prompt with tool results
      (let* ((response-without-tools (ai-auto-complete-tools-get-response-without-tools original-response))
             ;; Improved continuation prompt with clearer instructions and conversation history
             (continuation-prompt
              (let ((history-text ""))
                ;; Build history text from the conversation history
                (when (ai-auto-complete-tool-state-history state)
                  (setq history-text "\n\n")
                  (dolist (msg (reverse (ai-auto-complete-tool-state-history state)))
                    (let ((role (car msg))
                          (content (cdr msg)))
                      (setq history-text (concat history-text
                                               (cond
                                                ((eq role 'user) "USER: ")
                                                ((eq role 'agent) (format "Agent %s: " (car content)))
                                                ((eq role 'tool-result) "Tool Results: ")
                                                (t "Assistant: "))
                                               (if (eq role 'agent) (cdr content) content)
                                               "\n\n")))))

                (format history-text "\n\nYou previously responded: \n\n%s and called tool(s). Here are the tool results:%s\n\n%s\n\nBased on these tool results, you should now provide a complete and helpful response that incorporates this information. You should NOT just acknowledge receiving the results or ask what to do next - you should actually use the information to fulfill the USER's request. Look at previous conversation history for context. Inform the USER when you are done processing tool results or are confused about what to do next or just need to inform or confirm something with the USER."
                        response-without-tools
                        tool-results
                        (if (> error-count 0)
                            (format "NOTE: %d of %d tool calls had errors."
                                    error-count (length tool-calls))
                          ""))))) ; removed 'All tool calls completed successfully.'


        ;; Add tool calls and results to conversation history
        (when (and (boundp 'ai-auto-complete--chat-history)
                   ai-auto-complete--chat-history)
          ;; Create a formatted string of tool calls and results for the history
          (let ((tool-history-entry (format "Tool calls executed:\n%s\n\n%s"
                                           tool-results
                                           (if (> error-count 0)
                                               (format "NOTE: %d of %d tool calls had errors."
                                                       error-count (length tool-calls))
                                             "")))) ; removed 'All tool calls completed successfully.'

            ;; Add the tool calls and results to the history as a 'tool-result entry
            (push (cons 'tool-result tool-history-entry) ai-auto-complete--chat-history)
            (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
              (message "[TOOLS-DEBUG] Added tool calls and results to conversation history")
              (message "[TOOLS-DEBUG] Current history has %d messages" (length ai-auto-complete--chat-history))
              ;; Debug the history content
              (let ((count 0))
                (dolist (hist-msg ai-auto-complete--chat-history)
                  (setq count (1+ count))
                  (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                           count
                           (type-of hist-msg)
                           (if (listp hist-msg) (car hist-msg) "unknown")))))))

        ;; Send the continuation prompt to the LLM
        (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
          (message "[TOOLS-DEBUG] Sending continuation prompt to LLM"))

        ;; Add more debug logging
        (message "[TOOLS-DEBUG] Continuation with agent-name: %s" (or agent-name "nil"))
        (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

        ;; Add error handling around the LLM call
        (condition-case err
            ;; Call the LLM with the continuation prompt
            (progn
              (message "[TOOLS-DEBUG] Calling ai-auto-complete-complete with agent-name: %s" (or agent-name "nil"))
              (message "[TOOLS-DEBUG] Passing history with %d messages"
                       (if (ai-auto-complete-tool-state-history state)
                           (length (ai-auto-complete-tool-state-history state))
                         0))

              ;; Add more detailed debug logging for history
              (when (and (boundp 'ai-auto-complete-tools-debug-mode)
                         ai-auto-complete-tools-debug-mode
                         (ai-auto-complete-tool-state-history state))
                (let ((count 0))
                  (dolist (msg (ai-auto-complete-tool-state-history state))
                    (setq count (1+ count))
                    (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                             count
                             (type-of msg)
                             (if (listp msg) (car msg) "unknown")))))
              ;; Get the updated conversation history
              (let ((updated-history (when (and (boundp 'ai-auto-complete--chat-history)
                                              ai-auto-complete--chat-history)
                                     ai-auto-complete--chat-history)))
                (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
                  (message "[TOOLS-DEBUG] Using updated conversation history with %d messages"
                           (if updated-history (length updated-history) 0)))

                (ai-auto-complete-complete
                 backend
                 continuation-prompt
                 updated-history  ;; Pass the updated conversation history with tool results
                 (lambda (continuation-response)
                   ;; Add debug logging
                   (message "[TOOLS-DEBUG] Continuation response callback called with agent-name: %s" (or agent-name "nil"))
                   ;; Handle the continuation response
                   (ai-auto-complete-tools-handle-continuation
                    continuation-response response-without-tools callback (1+ depth) agent-name))
                 agent-name)))

          ;; Handle any errors that occur during the LLM call
          (error
           (let ((error-msg (format "ERROR during LLM continuation call: %s"
                                    (error-message-string err))))
             (message "[TOOLS-ERROR] %s" error-msg)
             ;; Ensure we preserve the agent-name when calling the callback
             (if (and callback (functionp callback))
                 (funcall callback (concat response-without-tools
                                         "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                         "Please try again or simplify your request.]"))
               ;; If no callback, use the default callback with agent-name
               (ai-auto-complete-tools-default-callback
                (concat response-without-tools
                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                       "Please try again or simplify your request.]")
                agent-name)))))

        ;; Return nil to signal the end of this branch of the state machine
        nil))))

;; Handle the continuation response from the LLM
(defun ai-auto-complete-tools-handle-continuation (continuation-response response-without-tools callback depth &optional agent-name)
  "Handle CONTINUATION-RESPONSE from the LLM.
RESPONSE-WITHOUT-TOOLS is the original response without tool calls.
CALLBACK is the function to call with the final result.
DEPTH is the current recursion depth.
AGENT-NAME is the optional name of the agent making the request."
  ;; Add debug logging
  (message "[TOOLS-DEBUG] ai-auto-complete-tools-handle-continuation called with agent-name: %s" (or agent-name "nil"))
  (message "[TOOLS-DEBUG] Callback type: %s" (type-of callback))

  ;; Get the current conversation history
  (let ((current-history (when (and (boundp 'ai-auto-complete--chat-history)
                                   ai-auto-complete--chat-history)
                          ai-auto-complete--chat-history)))

    ;; Add debug logging for the conversation history
    (when (and (boundp 'ai-auto-complete-tools-debug-mode) ai-auto-complete-tools-debug-mode)
      (message "[TOOLS-DEBUG] handle-continuation has access to history with %d messages"
               (if current-history (length current-history) 0))
      ;; Debug the history content
      (let ((count 0))
        (dolist (hist-msg current-history)
          (setq count (1+ count))
          (message "[TOOLS-DEBUG] History item %d - Type: %s, Role: %s"
                   count
                   (type-of hist-msg)
                   (if (listp hist-msg) (car hist-msg) "unknown")))))
    ;; Check if the continuation response is an error
    (if (string-match-p "^Error:" continuation-response)
        (progn
          (message "Detected error in continuation response, not processing for tools: %s"
                   (substring continuation-response 0 (min 100 (length continuation-response))))
          ;; Ensure we preserve the agent-name when calling the callback
          (if (and callback (functionp callback))
              (funcall callback (concat response-without-tools
                                       "\n\n[ERROR: The AI encountered an error while processing tool results. "
                                       "Please try again or simplify your request.]"))
            ;; If no callback, use the default callback with agent-name
            (ai-auto-complete-tools-default-callback
             (concat response-without-tools
                    "\n\n[ERROR: The AI encountered an error while processing tool results. "
                    "Please try again or simplify your request.]")
             agent-name)))

    ;; Check if the continuation response has more tool calls
    (if (string-match-p "<tool name=" continuation-response)
        ;; If it does, process them with a new state machine
        (let ((new-tool-calls (ai-auto-complete-tools-parse-response continuation-response)))
          (if new-tool-calls
              ;; Pass the agent-name and updated history to the callback to ensure it's preserved
              (ai-auto-complete-tools-trampoline
               #'ai-auto-complete-tools-start-processing
               continuation-response
               (lambda (final-response)
                 ;; Ensure we preserve the agent-name when calling the callback
                 (if (and callback (functionp callback))
                     (funcall callback (concat response-without-tools "\n\n" final-response))
                   ;; If no callback, use the default callback with agent-name
                   (ai-auto-complete-tools-default-callback
                    (concat response-without-tools "\n\n" final-response)
                    agent-name)))
               agent-name
               (when (and (boundp 'ai-auto-complete--chat-history)
                         ai-auto-complete--chat-history)
                 ai-auto-complete--chat-history))  ;; Pass the updated conversation history
            ;; No tool calls found (shouldn't happen given the string-match above)
            ;; Ensure we preserve the agent-name when calling the callback
            (if (and callback (functionp callback))
                (funcall callback (concat response-without-tools "\n\n" continuation-response))
              ;; If no callback, use the default callback with agent-name
              (ai-auto-complete-tools-default-callback
               (concat response-without-tools "\n\n" continuation-response)
               agent-name))))
      ;; No more tool calls, return the combined response
      ;; Ensure we preserve the agent-name when calling the callback
      (if (and callback (functionp callback))
          (funcall callback (concat response-without-tools "\n\n" continuation-response))
        ;; If no callback, use the default callback with agent-name
        (ai-auto-complete-tools-default-callback
         (concat response-without-tools "\n\n" continuation-response)
         agent-name))))))

(provide 'tools/tools-state-machine)
;;; tools-state-machine.el ends here
